package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 📏 三垂直测量助手类 - 专业级实现
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u0000 52\u00020\u0001:\u00015B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0018\u001a\u00020\u0019J\u0006\u0010\u001a\u001a\u00020\u0013J\u0006\u0010\u001b\u001a\u00020\u0013J\u0006\u0010\u001c\u001a\u00020\nJ\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001f0\u001eJ\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00160\u001eJ\u0006\u0010!\u001a\u00020\u0006J\u001e\u0010\"\u001a\u00020\n2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\u00062\u0006\u0010&\u001a\u00020\u0006J\u0006\u0010\'\u001a\u00020\nJ\u0016\u0010(\u001a\u00020\u00132\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0003\u001a\u00020\u0004J\u0006\u0010\t\u001a\u00020\nJ\u0010\u0010)\u001a\u00020\n2\u0006\u0010*\u001a\u00020+H\u0002J\u000e\u0010,\u001a\u00020\n2\u0006\u0010*\u001a\u00020+J\u000e\u0010-\u001a\u00020\n2\u0006\u0010*\u001a\u00020+J\u0006\u0010.\u001a\u00020\u0013J\b\u0010/\u001a\u00020\u0013H\u0002J\u0014\u00100\u001a\u00020\u00132\f\u00101\u001a\b\u0012\u0004\u0012\u00020\u00130\u0012J\u000e\u00102\u001a\u00020\u00132\u0006\u00103\u001a\u00020\u0016J\u0006\u00104\u001a\u00020\u0019R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00160\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeVerticalMeasureHelper;", "", "()V", "bitmap", "Landroid/graphics/Bitmap;", "draggedPointIndex", "", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "isDraggingPoint", "", "isInitialized", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "", "Lcom/touptek/measurerealize/utils/ThreeVerticalMeasurement;", "selectedMeasurement", "addNewMeasurement", "", "cleanup", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurementData", "", "Lcom/touptek/measurerealize/utils/ThreeVerticalMeasurementData;", "getAllMeasurements", "getMeasurementCount", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "hasSelectedMeasurement", "init", "isInImageContentArea", "touchPoint", "Landroid/graphics/PointF;", "isNearAnyMeasurement", "isPointOnMeasurement", "onScaleChanged", "resetInteractionState", "setMeasurementUpdateCallback", "callback", "setSelectedMeasurement", "measurement", "startNewMeasurement", "Companion", "app_debug"})
public final class ThreeVerticalMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.ThreeVerticalMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "ThreeVerticalMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private static final float DEFAULT_LINE_LENGTH = 200.0F;
    private static final float DEFAULT_POINT_OFFSET = 100.0F;
    private final java.util.List<com.touptek.measurerealize.utils.ThreeVerticalMeasurement> measurements = null;
    private com.touptek.measurerealize.TpImageView imageView;
    private android.graphics.Bitmap bitmap;
    private boolean isInitialized = false;
    private com.touptek.measurerealize.utils.ThreeVerticalMeasurement selectedMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public ThreeVerticalMeasureHelper() {
        super();
    }
    
    /**
     * 🚀 初始化助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🔄 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 📏 开始新的三垂直测量 - 在屏幕中心创建默认测量
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 📏 添加新的三垂直测量（在测量模式下）
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 处理触摸事件 - 三区域触摸处理逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🔧 检查触摸点是否在图像内容区域
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 📊 获取所有测量数据（用于渲染）
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.ThreeVerticalMeasurementData> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🔄 缩放变化时的坐标同步
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 🎯 检查是否有点在测量上
     */
    public final boolean isPointOnMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🎯 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🎯 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 📊 获取所有测量
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.ThreeVerticalMeasurement> getAllMeasurements() {
        return null;
    }
    
    /**
     * 🔄 设置选中的测量
     */
    public final void setSelectedMeasurement(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.ThreeVerticalMeasurement measurement) {
    }
    
    /**
     * 🔄 清除所有选中状态
     */
    public final void clearSelection() {
    }
    
    /**
     * 🗑️ 删除选中的测量 - 与LineMeasureHelper保持一致
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔄 重置交互状态
     */
    private final void resetInteractionState() {
    }
    
    /**
     * 🧹 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeVerticalMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "DEFAULT_LINE_LENGTH", "DEFAULT_POINT_OFFSET", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}